import { PictureFramework } from '@/components/pages/homepage/PictureFramework'
import { MyContainer } from '@/components/ui/MyContainer'
import { GetResourceHashDocument, GetResourceHashQuery } from '@/graphqls/types'
import { useDevice } from '@/hooks/useDevice'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useMerchant } from '@/hooks/useMerchant'
import { usePreloadResource } from '@/hooks/usePreloadResource'
import { useResetUserAtom } from '@/hooks/useResetUserAtom'
import {
  resourceHashAtom,
  selectedEventIdAtom,
  selectedEventDetailAtom,
  selectedImageFrameAtom,
} from '@/stores'
import { useAtom } from 'jotai'
import { useEffect } from 'react'
import { EventItem } from '@/apis/types'
import _api from '@/apis/maze.api'
import _ajax from '@/utils/ajax'

function Index() {
  const [resourceHash] = useAtom(resourceHashAtom)
  const { resetAtom } = useResetUserAtom()
  const { fetchResources } = usePreloadResource()
  const { getDeviceInfo, updateDeviceInfo } = useDevice()
  const { getMerchantInfo } = useMerchant()
  const [, setSelectedEventId] = useAtom(selectedEventIdAtom)
  const [, setSelectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [, setSelectedFrame] = useAtom(selectedImageFrameAtom)

  const getResourceHash = useImperativeQuery<GetResourceHashQuery>(
    GetResourceHashDocument
  )

  /** 对比hash，更新资源包 */
  useEffect(() => {
    const timer = setInterval(async () => {
      try {
        const hashRes = await getResourceHash()
        if (hashRes?.data?.deviceQuery?.templateHash === resourceHash) return

        fetchResources()
      } catch (err) {
        console.log(err, 'hash')
      }
    }, 60 * 1000)
    return () => {
      clearInterval(timer)
    }
  }, [resourceHash])

  const setDefaultEvent = async () => {
    const res = await _ajax.get(_api.event_list)
    const data: EventItem[] = res?.data?.data?.data
    const eventId = data[0]?.id
    const frameDetail = data[0]?.event_frames?.[0]
    setSelectedEventId(eventId)
    setSelectedEventDetail({ ...data[0] })
    if (frameDetail) {
      setSelectedFrame(frameDetail)
    }
    console.log('eventId', eventId)
    localStorage.setItem('userChooseEventId', String(eventId))
  }

  useEffect(() => {
    // 后台配置的设备信息
    getDeviceInfo().catch(() => {})
    // 商户信息
    getMerchantInfo().catch(() => {})
    // 更新机器状态
    updateDeviceInfo()
    // 回到模板页后，重置用户数据atom
    resetAtom()
    // 设置默认活动
    setDefaultEvent()
  }, [])

  return (
    <MyContainer>
      <PictureFramework />
    </MyContainer>
  )
}

export default Index
