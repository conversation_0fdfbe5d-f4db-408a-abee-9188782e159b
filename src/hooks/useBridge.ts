export const useBridge = () => {
  /** 注入callback方法, 用于需要APP回调的地方 */
  const appCb = (cb: any, cbName?: string) => {
    if (typeof cb === 'function') {
      window.appCallbackId = (window.appCallbackId || 0) + 1
      const fnName = cbName || 'appcb' + window.appCallbackId

      window[fnName as any] = cb

      return fnName
    }
  }

  const postMessage = (cmd: string, params: Record<string, any>) => {
    if (window.magpiesBridge?.query) {
      window.magpiesBridge?.query(cmd, JSON.stringify(params))
    } else if (window.webkit?.messageHandlers?.execute) {
      window.webkit.messageHandlers.execute.postMessage(
        JSON.stringify({
          cmd,
          params,
        })
      )
    }
  }

  const printImage = (params: Record<string, any>) => {
    postMessage('printImage', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  const downImage = (params: Record<string, any>) => {
    postMessage('downImage', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  const machineStatus = (params: Record<string, any>) => {
    console.log('bridge post msg machineStatus:', params)
    postMessage('machineStatus', {
      ...params,
      callback: appCb(params.callback, 'machineStatusCb'),
    })
  }

  const networkStatus = (params: Record<string, any>) => {
    postMessage('networkStatus', {
      ...params,
      callback: appCb(params.callback, 'networkStatusCb'),
    })
  }

  const getVideos = (params: Record<string, any>) => {
    postMessage('cacheVideo', {
      ...params,
      callback: appCb(params.callback, 'cacheVideoCb'),
    })
  }

  const takeCannonLight = (params: Record<string, any>) => {
    postMessage('takeLight', {
      ...params,
      callback: appCb(params.callback, 'takeLightCb'),
    })
  }

  const appPreloadResource = (params: Record<string, any>) => {
    postMessage('preloadWebResource', {
      ...params,
      callback: appCb(params.callback, 'preloadWebResourceCb'),
    })
  }

  const showVideo = (params: Record<string, any>) => {
    postMessage('showVideo', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  const hiddenVideo = () => {
    postMessage('hiddenVideo', {})
  }

  const postDefaultVideos = (params: Record<string, any>) => {
    postMessage('defaultLoopVideos', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  const postGuideVideo = (params: Record<string, any>) => {
    postMessage('userPostLoading', {
      ...params,
      callback: appCb(params.callback),
    })
  }
  const postUserVideos = (params: Record<string, any>) => {
    postMessage('userPostVideo', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  // tabbar 导航
  // { index: 'home/mine'}
  const tabbarTo = (params: Record<string, any>) => {
    postMessage('tabbar', {
      ...params,
      callback: appCb(params.callback),
    })
  }
  // tabbar 显示 or 关闭
  // { visible: 1 / 0 }
  const tabbarVisible = (params: Record<string, any>) => {
    postMessage('tabbarVisible', {
      ...params,
      callback: appCb(params.callback),
    })
  }
  // 打开登录页
  const openLoginPage = (params: Record<string, any>) => {
    postMessage('login', {
      ...params,
      callback: appCb(params.callback),
    })
  }
  // 购买积分
  const buyPoints = (params: Record<string, any>) => {
    postMessage('buyPoints', {
      ...params,
      callback: appCb(params.callback),
    })
  }
  // 订阅
  const subscription = (params: Record<string, any>) => {
    postMessage('subscription', {
      ...params,
      callback: appCb(params.callback),
    })
  }

  return {
    /** 打印机打印 */
    printImage,
    /** 下载图片 */
    downImage,
    /** 机器状态 */
    machineStatus,
    /** 网络状态 */
    networkStatus,
    getVideos,
    /** 佳能相机调用闪光灯 */
    takeCannonLight,
    /** app端资源预加载函数 */
    appPreloadResource,
    /** 显示原生视频 */
    showVideo,
    /** 隐藏原生视频 */
    hiddenVideo,
    /** 系统投送视频 */
    postDefaultVideos,
    /** 引导视频 */
    postGuideVideo,
    /** 用户投送视频 */
    postUserVideos,
    /** tabbar 导航 */
    tabbarTo,
    /** tabbar 显示 or 关闭 */
    tabbarVisible,
    /** 打开登录页 */
    openLoginPage,
    /** 购买积分 */
    buyPoints,
    /** 订阅 */
    subscription,
  }
}
