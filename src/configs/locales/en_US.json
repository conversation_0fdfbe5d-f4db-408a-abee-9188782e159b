{"资源加载中，请稍候": "Loading resources, please wait", "资源加载失败": "Resource loading failed", "重新加载": "Reload", "未知错误，请稍后重试": "Unknown error, please try again later", "轮询失败，请稍后重试": "Polling failed, please try again later", "加载中，请稍候": "Loading, please wait", "生成AI照片": "Generate AI photo", "生成AI视频": "Generate AI video", "立即开拍": "Let's shoot", "首页": "Home", "女性": "Female", "儿童": "Children", "老年": "Elderly", "男性": "Male", "正确": "Correct", "确保双人面部皆正对镜头，眼睛看向摄像头，头部不倾斜，五官清晰无遮挡，表情自然。": "Ensure both faces are directly facing the camera, eyes looking at the lens, with heads upright, facial features clearly visible and unobstructed, and a natural expression.", "确保拍摄时面部正对镜头，眼睛看向摄像头，五官清晰无遮挡，表情自然，衣服上无人脸图案。": "Make sure your face is directly facing the camera, your eyes are looking at the camera, your facial features are clear and unobstructed, your expression is natural, and there are no facial patterns on your clothes", "错误": "Error", "照片人像站位与模板人像站位相反；使用侧脸，面部被口罩、眼镜、帽子等遮挡，且照片中含有干扰物品。": "The subject's position does not match the template's position; Side profiles, faces obscured by masks, glasses, hats, etc. Photos containing distracting items.", "使用侧脸，面部被口罩、眼镜、帽子等遮挡，且照片中含有干扰物品。": "Using a side profile, face covered by a mask, glasses, or hat, or having distracting objects in the photo", "请保持人物主体在虚线框内": "Please keep the subject within the dashed frame", "以获得最佳生成效果": "For the best generation results", "秒": "S", "拍摄倒计时": "Starting the countdown", "上传图片": "Upload image", "开始拍摄": "Start shooting", "重新上传": "Re-upload", "重新拍摄": "Retake", "剩余重拍次数：{{remainShotTime}}次": "Remaining retake times: {{remainShotTime}} times", "提示": "Tips", "您确认返回首页吗？": "Are you sure you want to return to the homepage?", "返回后本次流程结束且不予退款": "This will end the current process, and no refunds will be issued", "正在努力获取订单信息": "Attempting to retrieve order information", "上传的图片不符合要求": "Uploaded image does not meet requirements", "当前分类下还没有模板": "There are no templates under the current category", "去看看别的分类吧": "Go to see other categories", "* 此价格包含 {{imageNum}} 张 AI 图片以及 1 张打印费用": "* This price includes {{imageNum}} AI images and printing fee for 1 image", "* 此价格包含 {{imageNum}} 张 AI 图片": "* This price includes {{imageNum}} AI images", "当前分类下暂无模板": "No templates available in this category yet", "下单失败，请稍后重试": "Order failed, please try again later", "资源更新中，请稍候再试": "Resource updating, please try again later", "人脸检测失败": "Face detection failed", "图片检测失败": "Image detection failed", "图片上传失败，请稍后重试": "Image upload failed, please try again later", "作画任务创建失败，请稍后重试": "Failed to create task, please try again later", "立即生成": "Generate now", "使用微信扫描二维码上传图片": "Use WeChat to scan the QR code and upload the image", "取消上传": "Cancel upload", "您确认不需要打印，": "Are you sure you don't need to print", "直接结束流程并返回首页吗？": "and want to end the process and return to the homepage?", "打印数量大于剩余打印纸数量": "The number of prints exceeds the remaining paper quantity", "点击加印": "Click to reprint", "立即打印": "Print now", "使用打印模板": "Use print template", "微信扫码": "Scan the QR code", "领取所有电子照片": "to download photos", "仅拍照支付用户可取": "Only available for photo-taking payment users", "制作证件照": "Create ID photos", "3D 建模": "3D modeling", "打印照片": "Print photos", "返回首页": "Return to homepage", "取消": "Cancel", "确认": "Confirm", "页面加载失败，请稍后重试": "Page loading failed, please try again later", "超时提醒": "Timeout reminder", "因您长时间未操作": "Due to inactivity", "系统将在 {{second}}s 后自动返回首页": "the system will automatically return to the homepage in {{second}}s", "请注意：返回首页后所有操作记录都将丢失": "Please note: All operation records will be lost after returning to the homepage", "继续流程": "Continue", "服务出现问题": "Service problem", "抱歉，服务暂时出现问题。": "Sorry, the service is temporarily unavailable.", "请联系客服申请退款": "Please contact customer service to request a refund.", "我们将在 1-3 个工作日内处理。感谢您的理解与支持。": "We will process it within 1-3 business days. Thank you for your understanding and support", "请联系客服申请退款，我们将在 1-3 个工作日内处理。感谢您的理解与支持。": "Please contact customer service to request a refund. We will process it within 1-3 business days. Thank you for your understanding and support", "知道了": "Got it", "后自动返回首页": "Returning to the homepage", "注意": "Attention", "点击生成 3D 模型后，将生成一个 GLB 格式的 3D 文件。": "After clicking 'Generate 3D Model', a 3D file in GLB format will be created.", "3D 建模效果不支持直接打印，需保存后通过 “扫码领取电子相册”将 3D 文件下载至本地。": "3D modeling effects do not support direct printing. To download the 3D file, please save it and then use 'Scan QR Code to Receive Digital Album.'", "选择制作 3D 文件的原图": "Select the original image to create the 3D file", "生成 3D 模型（GLB 格式）": "Generate 3D model (GLB format)", "重新生成": "Regenerate", "保存（GLB 格式）": "Save (GLB format)", "请在左侧选择参数": "Please select parameters on the left", "生成 3D 效果预览": "Generate 3D effect preview", "正在生成中": "Generating", "关闭": "Close", "预计还需": "Estimated time remaining", "拖动旋转模型": "Drag to rotate the model", "选择制作证件照的原图": "Select image for creating ID photo", "请选择背景色": "Please select a background color", "请选择预设尺寸": "Please select a preset size", "生成证件照": "Generate ID photo", "保存证件照": "Save ID photo", "保存证件照后将使用证件照进行打印": "Once saved, the ID photo will be used for printing.", "生成证件照预览": "Generate ID photo preview", "证件照正在处理中": "ID photo is being processed", "请稍候": "Please wait", "单张文件": "Single file", "打印文件": "Print file", "模板名称": "Template name", "支付价格": "Payment price", "扫码支付后即可拍照": "<PERSON>an the QR code to pay before taking a photo", "仅支持微信支付": "WeChat Pay only", "后二维码过期": "QR code expires", "二维码已过期，请刷新": "QR code expired, please refresh", "取消支付": "Cancel payment", "赠送": "Gift", "图片": "Image", "数量": "Quantity", "费用": "Cost", "合计": "Total", "使用微信扫码支付开始打印": "Use WeChat to scan and pay to start printing", "请使用微信扫描右侧二维码": "Please use WeChat to scan the QR code on the right", "上传您手机中的图片": "Upload an image from your phone", "生成出错": "Generation error", "生成过程中出现了一点小问题": "Something's wrong during the generation process", "请重新生成": "Please regenerate", "作画轮询失败，请稍后重试": "Generation polling failed, please try again later", "您的浏览器不支持 video 标签。": "Your browser does not support the video tag.", "打印中，请稍后": "Printing, please wait", "打印失败": "Printing failed", "重新打印": "Reprint", "返回": "Back", "打印好像出错了，请重试": "It seems there was an error with printing, please try again", "若问题始终存在，请联系客服": "If the problem persists, please contact customer service", "打印完成": "Printing completed", "请取走您的照片": "Please take your photo", "系统将在": "The system will", "应用失败，请稍后重试": "Application failed, please try again later", "可用模板": "Available templates", "无模板": "No templates", "应用所有": "Apply all", "应用单张": "Apply single", "二维码加载中": "QR code loading", "请稍后": "Please wait", "二维码加载失败": "QR code loading failed", "请点击重试": "Please click to retry", "二维码已过期": "QR code has expired", "请点击刷新": "Please click to refresh", "等待生成": "Waiting for generation", "原图": "Original image", "3D效果": "3D effect", "您的请求正在排队处理中": "Your request is being processed in the queue", "预计等待时间": "Estimated wait time", "图片正在生成中": "Generating images", "抱歉，摄像头暂时出现问题。请您通过“上传图片”的方式继续操作。感谢您的理解与配合！": "We apologize; there is currently an issue with the camera. Please proceed by using the 'Upload Image' option. Thank you for your understanding and cooperation!", "您访问的链接无效，请检查链接是否正确": "The link you visited is invalid, please check if the link is correct", "未检测到资源包，请联系运营人员进行配置以继续使用": "Resource pack not detected. Please contact operations staff for configuration to continue use", "获取资源包失败，请重试": "Failed to retrieve resource pack, please try again", "摄像头未识别到，请稍后再试或联系工作人员": "Camera not recognized, please try again later or contact staff", "打印机未识别到，请稍后再试或联系工作人员": "Printer not recognized, please try again later or contact staff", "打印机纸张不足，请联系工作人员": "Printer paper is insufficient, please contact staff", "确定": "Confirm", "正在提交打印任务，请稍后": "Submitting print task, please wait", "打印任务已提交，": "Print task submitted,", "正在打印中，请留意打印机哦！": "Printing in progress; please keep an eye on the printer!", "如果打印失败，可以在系统相册中选择照片重新打印": "If printing fails, you can select the photo from the system album to reprint.", "保存至本地": "Save to local", "保存成功": "Saved successfully", "保存并打印照片": "Save and print photo", "示例模板站位：女(左) - 男(右)，": "Example template position: female (left) - male (right)", "照片站位：男(左) - 女(右)，与模板相反": "Photo position: male (left) - female (right), opposite to the template", "你的视频仅限用于本次投屏播放，不会用于其它用途": "Your video is only for this screen display and will not be used for other purposes", "将该视频投送到大屏展示": "Cast this video to the big screen for display", "本次图像由 AI 生成，可能与实际有所偏差，请悉知": "This image is AI-generated and may differ from reality. Please be aware.", "领取所有视频": "Claim all videos", "生成失败": "Generation failed", "视频正在生成中": "Video is being generated", "请稍后重试": "Please try again later", "默认打印一张你的视频高光时刻封面图": "By default, a cover image of your video’s highlight moment will be printed.", "打印的图片查看 AR 视频": "View AR video of the printed picture", "首页-扫一扫": "Home-Scan", "小程序": "Mini Program", "步骤二": "Step 2", "使用": "Use", "领取视频": "Claim video", "步骤一": "Step 1", "微信扫描右侧二维码": "Scan the QR code on the right using WeChat", "微信扫码领取视频": "Use WeChat to scan the QR code and claim the video", "横版": "Horizontal", "竖版": "Vertical", "选择UI布局": "Select UI Orientation", "选择您喜欢的风格": "Choose Your Favorite Style", "精彩即将呈现...": "Magic is happening...", "选择喜欢的照片（可多选）": "Pick whatever you like", "下一步": "Next", "打印": "Print", "发送到邮箱": "Send to email", "下载": "Download", "评价": "Rate", "输入 Email 接收图片": "Enter email to receive photo", "扫描二维码下载图片": "Scan the QR code to download", "你喜欢这张照片？": "Do you like this photo?", "请选择打印数量": "Please select print quantity", "完成": "Completed", "评价成功": "Rating successful", "发送成功": "<PERSON><PERSON> successfully", "请输入正确的 Email 地址": "Please enter a valid email address", "软件授权包已过期": "The license for this device has expired.", "是否绑定软件授权包？": "A valid license is detected. Apply to this device?", "拍摄指南": "Shooting Guide", "正对镜头": "Face the camera", "移除遮挡": "No masks or sunglasses", "光线充足": "Good lighting", "分享你的AI写真": "Send it. Share it. Love it.", "选择活动": "Choose Event", "链接已过期": "The link has expired.", "模板列表": "All in this Style", "图片正在生成": "Magic is happening ({{completed}}/{{all}})", "该设备所选择活动已过期，请修改活动结束时间或选择另一个活动后再试。": "The selected activity on this device has expired. Please modify the activity end date or select another activity before trying again.", "作画订单创建失败，请稍后重试": "Failed to create drawing order, please try again later", "扫码支付": "SCAN TO PAY", "手机扫码并在浏览器中打开支付链接": "Scan to open the link in your mobile browser", "总计": "Total", "张": "items", "支持": "Pay With", "使用优惠券": "Use Coupon", "获得优惠": "off with coupon", "继续": "Continue", "结算": "Checkout", "支付成功": "Payment successful", "支付失败": "Payment failed", "请输入优惠券码": "Please enter the coupon code", "已过期": "Expired", "请先登录": "Please login first", "详情": "Details"}