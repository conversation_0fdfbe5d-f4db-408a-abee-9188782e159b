import { PrintTemplateSize } from '@/graphqls/types'
import { compareVersion, isAndroidMirror } from '@/utils'
import Cookies from 'js-cookie'

export const IMAGE_CDN = 'https://webcdn.wujieai.com/mirror-client'

/** 设备的唯一ID，存放在token当中 */
export const DEVICE_TOKEN = 'device-token'

/** 轮询时间，单位：秒 */
export const POLL_TICK = 3

/** 订单默认过期时间，单位：秒 */
export const ORDER_EXPIRE_TIME = 120

/** 关注服务号二维码 默认过期时间，单位：秒 */
export const WECHAT_ACCOUNT_EXPIRE_TIME = 5 * 60

/** 付款后的页面 */
export const AFTER_PAY_ROUTES = ['/print', '/photo', '/result']

/** 订单类型 */
export const ORDER_TYPE = {
  PRINT: 'print',
  DRAW: 'draw',
}
/** 虚拟设备ID */
export const VIRTUAL_UID = 'virtual_uid'
/** 虚拟设备ID 临时 */
export const VIRTUAL_UID_TMP = 'virtual_uid_tmp'

/** 打印机类型 */
export const PRINTER_TYPE = {
  [PrintTemplateSize.SIX_INCHES]: 1,
  [PrintTemplateSize.A_FOUR]: 2,
}

/** app不缓存标识 */
export const APP_NO_CACHE_TAG = 'mirrorcacheignore=1'

/** 安卓版本从1.1.5开始支持资源预加载函数 */
export const supportAppPreloadResource = () =>
  isAndroidMirror() &&
  compareVersion(Cookies.get('version') || '', '1.1.5') >= 0

// 客户端语言
export const LANGUAGE = Cookies.get('language') || 'en-US'
