/**
 * 返回首页 + 弹窗提示
 */
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MyModal } from '../ui/MyModal'
import { PortalContainer } from '../ui/PortalContainer'
import { scaleAtom, screenOrientationAtom } from '@/stores'
import { useAtomValue } from 'jotai'
import { getScaleStyle, isWebApp } from '@/utils'
import { useTranslation } from 'react-i18next'

interface IProps {
  onShowDialog?: () => void
  onCustomBack?: () => void
  // 弹窗属性
  dialogTitle?: string
  dialogContent?: React.ReactNode
  dialogCancelName?: string
  dialogOkName?: string
  style?: React.CSSProperties
  getContainer?: () => HTMLElement
}
const BackToHome: React.FC<IProps> = ({
  onShowDialog,
  onCustomBack,
  dialogTitle,
  dialogContent,
  dialogCancelName = '取消',
  dialogOkName = '确定',
  style,
  getContainer,
}) => {
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)
  const scale = useAtomValue(scaleAtom)
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)

  return null
  return (
    <>
      <PortalContainer getContainer={getContainer}>
        <div
          className={
            'h-[4rem] px-6 rounded-r-full bg-[rgba(0,0,0,0.32)] flex items-center justify-center fixed left-0 top-[72px] z-20 ' +
            'text-white text-[1.5rem] leading-[2rem] font-bold cursor-pointer'
          }
          ga-data="backHome"
          onClick={() => {
            if (onCustomBack?.()) {
              return
            }
            if (onShowDialog?.()) {
              setOpen(true)
              return
            }
            navigate('/home')
          }}
          style={{
            ...getScaleStyle({
              transform: `scale(${scale})`,
              transformOrigin: '0 0',
            }),
            ...style,
          }}
        >
          {t('首页')}
        </div>
      </PortalContainer>
      <MyModal
        open={open}
        title={dialogTitle}
        content={dialogContent}
        cancelText={t(dialogCancelName)}
        okText={t(dialogOkName)}
        onCancel={() => {
          setOpen(false)
        }}
        width={screenOrientation.isLandScape ? 800 : '80vw'}
        onOk={() => {
          navigate('/home')
        }}
      />
    </>
  )
}
export default BackToHome
