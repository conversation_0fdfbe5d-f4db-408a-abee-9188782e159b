import classnames from 'classnames'
import { publicPreLoadSourceObj } from '@/configs/source'
import { SvgIcon } from './SvgIcon'

function MyPlayBtn({
  type,
  onClick,
  className,
}: {
  type: 'video' | 'image'
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void
  className?: string
}) {
  return (
    <div
      className={classnames(
        'absolute top-8 left-8 rounded-xl leading-none bg-[rgba(35,40,61,0.5)] shadow-[0_0_4.48px_0_rgba(0,0,0,0.4)] w-[3.125rem] h-[3.125rem] p-3 cursor-pointer phone:w-[5rem] phone:h-[5rem]',
        className
      )}
      onClick={onClick}
    >
      <SvgIcon
        src={
          type === 'video'
            ? publicPreLoadSourceObj.play
            : publicPreLoadSourceObj.zoom
        }
        svgClassName="w-full h-full"
      />
    </div>
  )
}

export default MyPlayBtn
