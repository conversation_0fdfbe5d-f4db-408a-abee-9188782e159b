import React, { useEffect, useMemo } from 'react'
import classNames from 'classnames'
import { Outlet } from 'react-router-dom'
import PreloadResource from './PreloadResource'
import styles from './index.module.css'
// import { GlobalTimer } from '../../business/GlobalTimer'
import { AnalyticsScript } from '@/components/business/AnalyticsScript'
import { useThemeConfig } from '@/hooks/useThemeConfig'
import { isIphone } from '@/utils'
import { useAtomValue } from 'jotai'
import { scaleAtom, screenOrientationAtom } from '@/stores'
import { useWeappTask } from '@/hooks/useWeappTask'
import Cookies from 'js-cookie'
import { VideoPlayerModal } from '@/components/pages/common/VideoPlayerModal'

export const Layout: React.FC = () => {
  const {
    themeConfig,
    setBodyBgStyle,
    injectGlobalStyles,
    removeGlobalStyles,
  } = useThemeConfig()
  const scale = useAtomValue(scaleAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)

  // useWeappTask(PrintTemplateSize.A_FOUR)
  // useWeappTask(PrintTemplateSize.SIX_INCHES)
  useWeappTask()

  useEffect(() => {
    injectGlobalStyles(themeConfig)
    return () => {
      removeGlobalStyles()
    }
  }, [themeConfig])

  useEffect(() => {
    setBodyBgStyle(themeConfig, screenOrientation.isPortrait)
  }, [themeConfig, screenOrientation])

  const isIphoneHasBar = useMemo(() => {
    return Cookies.get('statusHeight')
  }, [])

  return (
    <>
      <PreloadResource>
        <div
          className={classNames(
            styles.container,
            'w-[100vw] h-[100vh] phone:w-[100dvw] phone:h-[100dvh] pb-[2dvh]',
            isIphoneHasBar && 'pt-[6dvh] pb-[3.25dvh]'
          )}
        >
          <Outlet />
        </div>
        {/* {themeConfig.logoUrl && (
          <div
            className="fixed top-[72px] right-6"
            style={{
              ...getScaleStyle({
                transform: `scale(${scale})`,
                transformOrigin: 'top right',
              }),
            }}
          >
            <img
              src={themeConfig.logoUrl}
              alt="logo"
              className={classNames(
                'object-cover rounded-full',
                screenOrientation.isPortrait
                  ? 'w-[64px] h-[64px]'
                  : 'w-[96px] h-[96px]'
              )}
            />
          </div>
        )} */}
        {/* <GlobalTimer /> */}
        <VideoPlayerModal />
      </PreloadResource>
      <AnalyticsScript />
    </>
  )
}
