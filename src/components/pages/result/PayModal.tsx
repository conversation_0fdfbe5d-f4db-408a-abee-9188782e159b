import { useEffect, useState, useRef } from 'react'
import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { useAtomValue } from 'jotai'
import QRCode from 'qrcode.react'

import { screenOrientationAtom, selectedEventIdAtom } from '@/stores'
import { publicPreLoadSourceObj } from '@/configs/source'
import { Button } from '@/components/ui/shad/button'
import classNames from 'classnames'
import { Input } from '@/components/ui/shad/input'
import _api from '@/apis/maze.api'
import _ajax from '@/utils/ajax'
import { useDevice } from '@/hooks/useDevice'
import { MirrorLoading } from 'wujieai-react-icon'
import { OrderStatusEnum } from '@/apis/types'
import { isPhone, isWebApp } from '../../../utils/ua'
import { toast } from '@/components/ui/shad/use-toast'
import { SvgIcon } from '@/components/ui/SvgIcon'

export const PayModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  afterPay: () => void
  currentImages: number[]
}> = ({ open, setOpen, currentImages, afterPay }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [isCouponOpen, setIsCouponOpen] = useState<boolean>(false)
  const [couponCode, setCouponCode] = useState<string>('')
  const [orderInfo, setOrderInfo] = useState<any>(null)
  const [orderStatus, setOrderStatus] = useState<OrderStatusEnum | null>(null)
  const pollTimerRef = useRef<number | null>(null)

  const { getDefaultDeviceInfo } = useDevice()
  const selectedEventId = useAtomValue(selectedEventIdAtom)

  useEffect(() => {
    if (open) {
      createOrder()
    } else {
      reset()
    }
    // 组件卸载时清理定时器
    return () => {
      if (pollTimerRef.current) {
        clearTimeout(pollTimerRef.current)
        pollTimerRef.current = null
      }
    }
  }, [open])

  const reset = () => {
    setOrderInfo(null)
    setCouponCode('')
    setIsCouponOpen(false)
    setOrderStatus(null)
    // 清理轮询定时器
    if (pollTimerRef.current) {
      clearTimeout(pollTimerRef.current)
      pollTimerRef.current = null
    }
  }

  const createOrder = async () => {
    try {
      const deviceInfo = await getDefaultDeviceInfo()
      const data = {
        cancel_url: `${window.location.origin}/payment/fail`,
        success_url: `${window.location.origin}/payment/success`,
        count: currentImages.length,
        coupon_code: couponCode?.trim(),
        device_id: deviceInfo?.id,
        event_id: selectedEventId,
        pre_order_id: couponCode?.trim() ? (orderInfo?.order_id ?? 0) : 0,
      }
      const res = await _ajax.post(_api.create_image_payment_order, data)
      if (res.data.code === 200) {
        setOrderInfo(res.data.data)
      } else if (res.data.code === 1086 || res.data.code === 1087) {
        setIsCouponOpen(false)
        setCouponCode('')
        toast({
          description: res.data.msg,
        })
      }
      console.log('create_order', res)
    } catch (error) {
      console.log('create_order_error', error)
    }
  }

  const handleCouponSubmit = () => {
    if (!couponCode) {
      toast({
        description: t('请输入优惠券码'),
      })
      return
    }
    if (pollTimerRef.current) {
      clearTimeout(pollTimerRef.current)
      pollTimerRef.current = null
    }
    createOrder()
    setIsCouponOpen(true)
  }

  const getOrderInfo = async () => {
    try {
      const res = await _ajax.post(_api.get_image_payment_order, {
        order_id: orderInfo?.order_id,
      })
      if (res.data.data?.status === OrderStatusEnum.PAID) {
        setOrderStatus(OrderStatusEnum.PAID)
        // 支付成功后的延迟回调
        window.setTimeout(() => {
          afterPay()
        }, 3_000)
      } else if (res.data.data?.status === OrderStatusEnum.EXPIRED) {
        setOrderStatus(OrderStatusEnum.EXPIRED)
      } else if (res.data.data?.status === OrderStatusEnum.PROCESSING) {
        // 将轮询定时器保存到ref中，以便后续清除
        pollTimerRef.current = window.setTimeout(() => {
          getOrderInfo()
        }, 3_000)
      } else {
        setOrderStatus(OrderStatusEnum.FAILED)
      }
      console.log('get_order_info', res)
    } catch (error) {
      console.log('get_order_info_error', error)
    }
  }

  const handlePayment = () => {
    if (orderInfo) {
      window.open(orderInfo.url)
    }
  }

  const handleRefresh = () => {
    setOrderStatus(null)
    if (pollTimerRef.current) {
      clearTimeout(pollTimerRef.current)
      pollTimerRef.current = null
    }
    createOrder()
  }

  useEffect(() => {
    if (orderInfo) {
      getOrderInfo()
    }
  }, [orderInfo])

  return (
    <>
      <MyModal
        open={!!orderStatus}
        width={screenOrientation.isLandScape ? 600 : '58vw'}
        content={
          <div className="flex-1 py-12 flex flex-col items-center justify-center">
            <img
              className="w-[10rem]"
              src={
                publicPreLoadSourceObj[
                  orderStatus === OrderStatusEnum.PAID
                    ? 'paySuccess'
                    : 'payFail'
                ]
              }
              alt=""
            />
            <h1 className="text-[2.8rem] text-center font-semibold leading-[2rem] py-12 mt-8">
              {t(
                orderStatus === OrderStatusEnum.PAID ? '支付成功' : '支付失败'
              )}
            </h1>
          </div>
        }
        showCancelButton={false}
        showOkButton={false}
        onCancel={() => setOrderStatus(null)}
        contentClassName="p-0 w-full"
      />
      <MyModal
        open={open}
        width={screenOrientation.isLandScape ? 900 : '90vw'}
        content={
          <div className="flex-1 pt-6">
            <h1 className="text-[2.8rem] text-center font-semibold leading-[2rem]">
              {isWebApp() ? t('结算') : t('扫码支付')}
            </h1>
            {!isWebApp() && (
              <>
                <p className="py-6 text-[1.825rem] text-center font-normal leading-[1.825rem]">
                  {t('手机扫码并在浏览器中打开支付链接')}
                </p>
                <div className="flex py-6 justify-center">
                  {orderInfo ? (
                    <div className="qrcode-wrap relative rounded-[16px] bg-neutral-900 shadow-box-neutral p-6 inline-block">
                      <QRCode
                        value={orderInfo?.url}
                        fgColor="#000"
                        size={isPhone() ? 160 : 350}
                      />
                      {orderStatus === OrderStatusEnum.EXPIRED && (
                        <div
                          className="absolute flex items-center justify-center flex-col top-0 left-0 w-full h-full z-[100] bg-black/60"
                          onClick={handleRefresh}
                        >
                          <SvgIcon
                            src="/images/icons/reload.svg"
                            className="w-16 h-16 cursor-pointer"
                          />
                          <span className="text-white text-[1.625rem] mt-2">
                            {t('已过期')}
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-[350px] h-[350px] rounded-[16px] my-6 bg-black/30 flex items-center justify-center iphone:w-[160px] iphone:h-[160px]">
                      <MirrorLoading className="w-10 h-10 animate-spin opacity-60" />
                    </div>
                  )}
                </div>
              </>
            )}
            <div
              className={classNames(
                'text-center font-semibold text-[2.125rem] ',
                {
                  'py-28': isWebApp(),
                  'py-6': !isWebApp(),
                }
              )}
            >
              {!isWebApp() && <span>{t('总计')}</span>}
              <span>
                <span
                  className={classNames('text-[2.825rem] px-2', {
                    'text-[5rem]': isWebApp(),
                  })}
                >
                  ${orderInfo?.amount}
                </span>
                {orderInfo?.origin_amount !== orderInfo?.amount && (
                  <i className="text-white/40 line-through not-italic">
                    ${orderInfo?.origin_amount}
                  </i>
                )}
                ( {currentImages.length} {t('张')})
              </span>
            </div>
            <div
              className={classNames(
                'flex justify-between items-center gap-6 w-[38rem] mx-auto border-3 border-white/30 rounded-full p-2',
                {
                  ['maze-bg-gradient-btn border-white/100 border-2']:
                    isCouponOpen,
                }
              )}
            >
              {!isCouponOpen ? (
                <>
                  <Input
                    value={couponCode}
                    onChange={e => setCouponCode(e.target.value)}
                    className="flex-1 border-transparent h-[3.625rem] text-[2rem] text-center !outline-none bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                  <Button
                    variant="outline"
                    size="md"
                    onClick={handleCouponSubmit}
                    className="w-auto maze-bg-gradient-btn border-2"
                  >
                    {t('使用优惠券')}
                  </Button>
                </>
              ) : (
                <span className="flex-1 text-center text-[2rem] leading-[3.625rem]">
                  🎉 $
                  {(orderInfo?.origin_amount - orderInfo?.amount).toFixed(2)}{' '}
                  {t('获得优惠')}
                </span>
              )}
            </div>
            {isWebApp() && (
              <div className="flex justify-center">
                <Button
                  className="mt-10 w-[38rem]"
                  size="lg"
                  onClick={handlePayment}
                  disabled={!orderInfo}
                >
                  {t('继续')}
                </Button>
              </div>
            )}
            <div className="flex justify-center items-center gap-5 mt-10">
              <span className="text-[1.625rem] text-center font-semibold italic leading-[1.625rem]">
                {t('支持')}
              </span>
              <img
                className="h-[3rem]"
                src={publicPreLoadSourceObj.payMethod}
              />
            </div>
          </div>
        }
        showCancelButton={false}
        showOkButton={false}
        contentClassName="p-0 w-full"
        onCancel={() => {
          // 关闭弹窗时清除定时器
          if (pollTimerRef.current) {
            clearTimeout(pollTimerRef.current)
            pollTimerRef.current = null
          }
          setOpen?.(false)
        }}
      />
    </>
  )
}
