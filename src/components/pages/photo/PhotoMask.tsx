import { CDNImage } from '@/components/ui/CDNImage'
import { ScreenOrientation } from '@/stores/types'

export const PhotoMask: React.FC<{
  multiple: boolean
  className?: string
  direction?: ScreenOrientation
}> = ({ multiple, className }) => {
  console.log('is multiple', multiple)
  return (
    <>
      <CDNImage
        className={`absolute left-0 bottom-0 right-0 top-0 rounded-none object-cover ${className}`}
        src={'/images/photo/mask-phone.png'}
      />
    </>
  )
}
