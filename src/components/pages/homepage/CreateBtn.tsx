import { Button } from '@/components/ui/shad/button'
import { ShootTipModal } from './ShootTipModal'
import { ActiveTemplateItem } from './const'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import { useBridge } from '@/hooks/useBridge'
import Cookies from 'js-cookie'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { SvgIcon } from '@/components/ui/SvgIcon'

export const CreateBtn: React.FC<{
  selectTemplateList: ActiveTemplateItem[]
  isPortrait?: boolean
  activeGender?: string
}> = ({ selectTemplateList }) => {
  const [tipModalOpen, setTipModalOpen] = useState(false)

  const { t } = useTranslation()
  const { openLoginPage, subscription } = useBridge()

  const { data: userInfo } = useSWR(
    Cookies.get('token') ? _api.get_user : null,
    url => _ajax.get(url)
  )

  const userAvailablePoint = userInfo?.data?.data?.point ?? 0
  const drawNeedPoint = useMemo(() => {
    return selectTemplateList?.length * 4
  }, [selectTemplateList])
  const isPointEnough = useMemo(() => {
    return userAvailablePoint >= drawNeedPoint
  }, [userAvailablePoint, drawNeedPoint])

  // 作图
  const handlePrePayOrder = () => {
    setTipModalOpen(true)
  }

  const handlePointBuy = () => {
    console.log('point 不足', drawNeedPoint, userAvailablePoint)
    subscription({})
  }

  return (
    <>
      <div className="flex flex-col items-center">
        <Button
          size="lg"
          className={classNames(
            'flex maze-bg-gradient-btn w-[80dvw] h-[9.375rem] text-[3.125rem] rounded-[150px] gap-[8px]'
          )}
          ga-data="preOrder"
          variant="outline"
          onClick={() => {
            if (!Cookies.get('token')) {
              openLoginPage({})
              return
            }
            if (!isPointEnough) {
              handlePointBuy()
              return
            }
            handlePrePayOrder()
          }}
        >
          {t('Generate')}
          <span className="flex items-center gap-2">
            <SvgIcon
              src="/images/icons/point.svg"
              className="w-[24px] h-[24px]"
            />
            {drawNeedPoint}
          </span>
        </Button>
      </div>
      <ShootTipModal
        open={tipModalOpen}
        setOpen={value => setTipModalOpen(value)}
      />
    </>
  )
}
