import { TemplateListProps } from './const'
import SingleTemplate from './SingleTemplate'
import { useNavigate } from 'react-router-dom'
import { ThemeDetail } from '@/apis/types'
import { MirrorLoading } from 'wujieai-react-icon'

/** 多行模版列表 - 简单平铺布局 */
const MultipleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  activeGender,
}: TemplateListProps) => {
  const navigate = useNavigate()

  // 处理模型详情页面跳转
  const handleModelDetailClick = (template: ThemeDetail) => {
    console.log('点击模板:', template)
    // 先设置选中的模板，这会触发 PictureFramework 中的 useEffect 来更新 selectedThemeDetailAtom
    setActiveTemplate(template)
    // 使用 setTimeout 确保状态更新完成后再导航
    setTimeout(() => {
      navigate(`/models?id=${template.id}`)
    }, 0)
  }

  return (
    <>
      {selectTemplateList && selectTemplateList.length > 0 ? (
        <div className="grid grid-cols-2 gap-8 py-2 pb-[32px]">
          {selectTemplateList.map(template => (
            <div key={template.id} className="w-full">
              <SingleTemplate
                isMultiple
                activeGender={activeGender}
                item={template}
                active={template.id === activeTemplate?.id}
                onSelect={() => handleModelDetailClick(template)}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 my-[34px] p-4 text-center">
          <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
        </div>
      )}
    </>
  )
}
export default MultipleTemplateList
