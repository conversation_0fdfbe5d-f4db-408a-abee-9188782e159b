import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import {
  screenOrientationAtom,
  isShowThemeDetailModalAtom,
  selectedThemeDetailAtom,
  selectedGenderAtom,
  selectedEventDetailAtom,
} from '@/stores'
import { useEffect, useMemo, useState } from 'react'
import MultipleTemplateList from '@/components/pages/homepage/MultipleTemplateList'
import classNames from 'classnames'
import { ThemeDetailModal } from './ThemeDetailModal'
import { ThemeDetail } from '@/apis/types'
import { AutoScroll } from '@/components/ui/AutoScroll'
import { useBridge } from '@/hooks/useBridge'
import { SvgIcon } from '@/components/ui/SvgIcon'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import Cookies from 'js-cookie'

export const PictureFramework = () => {
  const screenOrientation = useAtomValue(screenOrientationAtom)

  // 选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | undefined | null
  >()
  // 选中的性别
  const [activeGender] = useAtom(selectedGenderAtom)
  // 选中的 tag id
  const [activeTab, setActiveTab] = useState<number>(0)

  const [themeDetailModalOpen, setThemeDetailModalOpen] = useAtom(
    isShowThemeDetailModalAtom
  )

  const setSelectedThemeDetail = useSetAtom(selectedThemeDetailAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)

  const { tabbarVisible } = useBridge()

  const { data: userInfo } = useSWR(
    Cookies.get('token') ? _api.get_user : null,
    url => _ajax.get(url),
    {
      refreshInterval: 3000,
    }
  )

  useEffect(() => {
    tabbarVisible({ visible: 1 })
  }, [])

  useEffect(() => {
    if (activeTemplate) {
      console.log('设置选中的主题详情:', activeTemplate)
      setSelectedThemeDetail(activeTemplate)
    }
  }, [activeTemplate, setSelectedThemeDetail])

  const categoryList = useMemo(() => {
    return (
      selectedEventDetail?.tags?.map((it, i) => ({
        label: it.name as string,
        value: i as number,
        id: it.id as number,
      })) || []
    )
  }, [selectedEventDetail])

  // 使用所有分类下的模版并去重
  const selectTemplateList = useMemo(() => {
    const selectedThemes = selectedEventDetail?.tags?.[activeTab]?.themes || []
    return selectedThemes
  }, [selectedEventDetail, activeTab])

  // 设置默认选中模板
  useEffect(() => {
    setActiveTemplate(selectTemplateList?.[0])
  }, [selectTemplateList])

  return (
    <>
      <div className="w-full h-full p-[16px] pt-0">
        <div className="z-10 pt-[16px] bg-[#141828]">
          <div className="flex text-white w-full items-center justify-between leading-[18px]">
            <span className="text-[18px] text-white font-medium leading-none">
              AI Models
            </span>
            {userInfo?.data?.data && (
              <span className="text-[14px] flex items-center gap-2 border-[1px] border-[#444B5F] rounded-[8px] px-[12px] py-[4px]">
                <SvgIcon
                  className="w-[22px] inline-block"
                  src="/images/icons/point.svg"
                />
                {userInfo?.data?.data?.point}
              </span>
            )}
          </div>
          {categoryList.length > 1 && (
            <div className={classNames('mt-8 mx-auto w-full')}>
              <AutoScroll
                activeIndex={categoryList.findIndex(
                  it => it.value === activeTab
                )}
                wrapClassName="!justify-start"
                className=""
                listKey="categoryList"
              >
                <div className="flex gap-[16px]">
                  {categoryList?.map(it => (
                    <div
                      className={classNames(
                        'text-[14px] font-medium pb-[6px] cursor-pointer whitespace-nowrap leading-none text-white',
                        {
                          categoryItemActive: it.value === activeTab,
                        }
                      )}
                      key={it.value}
                      onClick={() => setActiveTab(it.value)}
                    >
                      {it.label}
                    </div>
                  ))}
                </div>
              </AutoScroll>
            </div>
          )}
        </div>
        <MultipleTemplateList
          activeGender={activeGender}
          activeTemplate={activeTemplate}
          setActiveTemplate={setActiveTemplate}
          selectTemplateList={selectTemplateList}
          listKey={`${activeGender}-${activeTab}`}
          multiline={screenOrientation.isPortrait}
        />
      </div>
      <ThemeDetailModal
        activeGender={activeGender}
        open={themeDetailModalOpen}
        setOpen={setThemeDetailModalOpen}
        themeDetail={activeTemplate}
      />
    </>
  )
}
