.template {
    border: 2px solid #fff;
    overflow: hidden;
    &.active {
        @apply border-white mx-3 scale-100;
    }
    &.multiple {
        @apply w-[38.88vw] scale-100 mx-0 ;
    }
}

/* 缩略图导航样式 */
:global(.thumbnail-swiper) {
    padding: 0 !important;
}

:global(.thumbnail-swiper .swiper-slide) {
    width: auto !important;
    flex-shrink: 0;
}

/* 缩略图容器样式 */
.thumbnailContainer {
    @apply p-[6px];
}

/* 缩略图项目样式 */
.thumbnailItem {
    @apply relative  cursor-pointer transition-all duration-300;
    border: 2px solid #fff;
    border-radius: 6px;
    &.active {
        box-shadow: 0px 0px 10.996px 0px rgba(104, 245, 255, 0.60), 0px 0px 43.983px 0px #9565FF;
    }
    &.inactive {
        @apply scale-100 opacity-70;
    }
}
